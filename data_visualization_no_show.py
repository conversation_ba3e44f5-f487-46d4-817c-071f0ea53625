#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化期末考核任务 - 无显示版本（只保存图片）
为3个数据集创建9种不同类型的可视化图表
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from wordcloud import WordCloud
from docx import Document
import jieba
import re
from collections import Counter
import warnings
import platform
import os
warnings.filterwarnings('ignore')

# 强制设置中文字体
def setup_chinese_fonts():
    """强制设置中文字体"""
    print("正在配置中文字体...")

    if platform.system() == 'Windows':
        font_paths = [
            'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
            'C:/Windows/Fonts/simhei.ttf',    # 黑体
            'C:/Windows/Fonts/simsun.ttc',    # 宋体
        ]

        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    fm.fontManager.addfont(font_path)
                    font_prop = fm.FontProperties(fname=font_path)
                    font_name = font_prop.get_name()
                    print(f"成功添加字体: {font_name}")
                    plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                    break
                except Exception as e:
                    print(f"添加字体失败: {font_path}")
                    continue

    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10

    print("中文字体配置完成!")

# 执行字体设置
setup_chinese_fonts()

class DataVisualizerNoShow:
    def __init__(self):
        self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
                      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9']

    def load_data(self):
        """加载所有数据集"""
        self.population_df = pd.read_excel("数据可视化数据集-A/2022年北京市各行政区常住人口.xlsx")
        self.sales_df = pd.read_excel("数据可视化数据集-A/产品销售统计表.xlsx")

        doc = Document("数据可视化数据集-A/2023年国务院政府工作报告.docx")
        self.report_text = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                self.report_text.append(paragraph.text.strip())

        print("数据加载完成！")

    def create_population_charts(self):
        """创建人口数据的3个图表"""
        print("创建人口数据图表...")

        # 图表1: 簇状柱形图
        plt.figure(figsize=(14, 8))

        x = np.arange(len(self.population_df))
        width = 0.35

        plt.bar(x - width/2, self.population_df['常住人口（万人）'],
               width, label='常住人口', color=self.colors[0], alpha=0.8)
        plt.bar(x + width/2, self.population_df['常住外来人口（万人）'],
               width, label='常住外来人口', color=self.colors[1], alpha=0.8)

        plt.xlabel('行政区', fontsize=12, fontweight='bold')
        plt.ylabel('人口数量（万人）', fontsize=12, fontweight='bold')
        plt.title('2022年北京市各行政区常住人口与外来人口对比', fontsize=16, fontweight='bold')
        plt.xticks(x, self.population_df['行政区'], rotation=45, ha='right')
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('图表1_北京人口簇状柱形图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 图表2: 饼图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

        total_population = self.population_df['常住人口（万人）'].sum()
        sizes1 = self.population_df['常住人口（万人）']
        labels = self.population_df['行政区']

        threshold = 0.05
        large_areas = sizes1 / total_population >= threshold

        plot_sizes1 = sizes1[large_areas].tolist()
        plot_labels1 = labels[large_areas].tolist()
        if not large_areas.all():
            plot_sizes1.append(sizes1[~large_areas].sum())
            plot_labels1.append('其他区域')

        ax1.pie(plot_sizes1, labels=plot_labels1, autopct='%1.1f%%',
               colors=self.colors[:len(plot_sizes1)], startangle=90)
        ax1.set_title('2022年北京市各区常住人口占比', fontsize=14, fontweight='bold')

        total_foreign = self.population_df['常住外来人口（万人）'].sum()
        sizes2 = self.population_df['常住外来人口（万人）']

        large_areas2 = sizes2 / total_foreign >= threshold
        plot_sizes2 = sizes2[large_areas2].tolist()
        plot_labels2 = labels[large_areas2].tolist()
        if not large_areas2.all():
            plot_sizes2.append(sizes2[~large_areas2].sum())
            plot_labels2.append('其他区域')

        ax2.pie(plot_sizes2, labels=plot_labels2, autopct='%1.1f%%',
               colors=self.colors[:len(plot_sizes2)], startangle=90)
        ax2.set_title('2022年北京市各区常住外来人口占比', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig('图表2_北京人口饼图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 图表3: 散点图
        plt.figure(figsize=(12, 8))

        plt.scatter(self.population_df['常住人口（万人）'],
                   self.population_df['常住外来人口（万人）'],
                   s=200, c=self.colors[2], alpha=0.7, edgecolors='black', linewidth=1)

        for i, txt in enumerate(self.population_df['行政区']):
            plt.annotate(txt, (self.population_df['常住人口（万人）'].iloc[i],
                             self.population_df['常住外来人口（万人）'].iloc[i]),
                        xytext=(5, 5), textcoords='offset points', fontsize=10)

        z = np.polyfit(self.population_df['常住人口（万人）'],
                      self.population_df['常住外来人口（万人）'], 1)
        p = np.poly1d(z)
        plt.plot(self.population_df['常住人口（万人）'],
                p(self.population_df['常住人口（万人）']),
                "r--", alpha=0.8, linewidth=2)

        plt.xlabel('常住人口（万人）', fontsize=12, fontweight='bold')
        plt.ylabel('常住外来人口（万人）', fontsize=12, fontweight='bold')
        plt.title('北京市各区常住人口与外来人口关系分析', fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3)

        correlation = np.corrcoef(self.population_df['常住人口（万人）'],
                                self.population_df['常住外来人口（万人）'])[0,1]
        plt.text(0.05, 0.95, f'相关系数: {correlation:.3f}',
                transform=plt.gca().transAxes, fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

        plt.tight_layout()
        plt.savefig('图表3_北京人口散点图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("人口数据图表创建完成！")

    def create_sales_charts(self):
        """创建销售数据的3个图表"""
        print("创建销售数据图表...")

        # 图表4: 折线图
        plt.figure(figsize=(14, 8))

        sales_sorted = self.sales_df.sort_values('日期')

        plt.plot(sales_sorted['日期'], sales_sorted['销售额（元）'],
                marker='o', linewidth=2.5, markersize=6, color=self.colors[3])

        plt.xlabel('日期', fontsize=12, fontweight='bold')
        plt.ylabel('销售额（元）', fontsize=12, fontweight='bold')
        plt.title('2023年1月产品销售额时间趋势分析', fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3)

        import matplotlib.dates as mdates
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=3))
        plt.xticks(rotation=45)

        avg_sales = sales_sorted['销售额（元）'].mean()
        plt.axhline(y=avg_sales, color='red', linestyle='--', alpha=0.7, linewidth=2)
        plt.text(0.02, 0.95, f'平均销售额: {avg_sales:.0f}元',
                transform=plt.gca().transAxes, fontsize=12,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

        plt.tight_layout()
        plt.savefig('图表4_销售额折线图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 图表5: 箱形图
        plt.figure(figsize=(12, 8))

        self.sales_df['商品类别'] = self.sales_df['商品编码'].str[0]

        categories = self.sales_df['商品类别'].unique()
        box_data = []
        labels = []

        for cat in sorted(categories):
            cat_data = self.sales_df[self.sales_df['商品类别'] == cat]['销售单价（元）']
            box_data.append(cat_data)
            labels.append(f'类别{cat}')

        bp = plt.boxplot(box_data, labels=labels, patch_artist=True, notch=True, showmeans=True)

        for patch, color in zip(bp['boxes'], self.colors[:len(categories)]):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)

        plt.xlabel('商品类别', fontsize=12, fontweight='bold')
        plt.ylabel('销售单价（元）', fontsize=12, fontweight='bold')
        plt.title('不同商品类别销售单价分布分析', fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('图表5_商品价格箱形图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 图表6: 气泡图
        plt.figure(figsize=(12, 8))

        scatter = plt.scatter(self.sales_df['销售数量'],
                            self.sales_df['销售单价（元）'],
                            s=self.sales_df['销售额（元）']/10,
                            c=self.sales_df['商品类别'].astype('category').cat.codes,
                            cmap='viridis', alpha=0.6, edgecolors='black', linewidth=1)

        plt.xlabel('销售数量', fontsize=12, fontweight='bold')
        plt.ylabel('销售单价（元）', fontsize=12, fontweight='bold')
        plt.title('销售数量、单价与销售额关系分析（气泡大小代表销售额）', fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3)

        cbar = plt.colorbar(scatter)
        cbar.set_label('商品类别', fontsize=11)

        plt.text(0.02, 0.98, '气泡大小 ∝ 销售额',
                transform=plt.gca().transAxes, fontsize=11,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

        plt.tight_layout()
        plt.savefig('图表6_销售关系气泡图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("销售数据图表创建完成！")

    def create_text_charts(self):
        """创建文本数据的3个图表"""
        print("创建文本数据图表...")

        # 合并所有文本
        full_text = ' '.join(self.report_text)

        # 文本预处理
        clean_text = re.sub(r'[^\u4e00-\u9fff]', ' ', full_text)

        # 分词
        words = jieba.lcut(clean_text)

        # 过滤停用词
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
                     '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
                     '自己', '这', '年', '月', '日', '个', '中', '为', '与', '及', '等', '将', '对',
                     '从', '把', '向', '以', '由', '用', '被', '让', '使', '得', '能', '可', '应',
                     '更', '加', '进一步', '继续', '坚持', '推进', '加强', '提高', '完善', '建设'}

        filtered_words = [word for word in words if len(word) >= 2 and word not in stop_words]

        # 统计词频
        word_freq = Counter(filtered_words)
        top_words = word_freq.most_common(20)

        # 图表7: 词云图
        plt.figure(figsize=(14, 8))

        # 尝试多个字体路径
        font_paths = [
            'C:/Windows/Fonts/simhei.ttf',
            'C:/Windows/Fonts/msyh.ttc',
            'C:/Windows/Fonts/simsun.ttc'
        ]

        wordcloud = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    wordcloud = WordCloud(
                        font_path=font_path,
                        width=1200, height=600,
                        background_color='white',
                        max_words=100,
                        colormap='viridis',
                        relative_scaling=0.5,
                        random_state=42
                    ).generate_from_frequencies(word_freq)
                    print(f"词云图使用字体: {font_path}")
                    break
                except Exception as e:
                    print(f"字体 {font_path} 失败: {e}")
                    continue

        if wordcloud is None:
            wordcloud = WordCloud(
                width=1200, height=600,
                background_color='white',
                max_words=100,
                colormap='viridis',
                relative_scaling=0.5,
                random_state=42
            ).generate_from_frequencies(word_freq)
            print("词云图使用默认字体")

        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('2023年国务院政府工作报告关键词云图', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.savefig('图表7_政府报告词云图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 图表8: 条形图
        plt.figure(figsize=(12, 8))

        words_list = [item[0] for item in top_words[:15]]
        counts_list = [item[1] for item in top_words[:15]]

        bars = plt.barh(range(len(words_list)), counts_list, color=self.colors[:len(words_list)])

        plt.yticks(range(len(words_list)), words_list)
        plt.xlabel('词频', fontsize=12, fontweight='bold')
        plt.ylabel('关键词', fontsize=12, fontweight='bold')
        plt.title('政府工作报告高频词汇统计（Top 15）', fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3, axis='x')

        for i, (bar, count) in enumerate(zip(bars, counts_list)):
            plt.text(bar.get_width() + 0.5, bar.get_y() + bar.get_height()/2,
                    str(count), ha='left', va='center', fontsize=10)

        plt.tight_layout()
        plt.savefig('图表8_高频词汇条形图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 图表9: 环形图
        plt.figure(figsize=(10, 10))

        themes = {
            '经济发展': ['经济', '发展', '增长', 'GDP', '投资', '消费', '市场', '产业', '企业', '金融'],
            '民生保障': ['民生', '就业', '教育', '医疗', '养老', '住房', '收入', '保障', '福利', '社会'],
            '改革创新': ['改革', '创新', '科技', '数字', '技术', '研发', '人才', '转型', '升级', '现代化'],
            '生态环境': ['生态', '环境', '绿色', '节能', '减排', '污染', '治理', '保护', '可持续', '碳'],
            '对外开放': ['开放', '合作', '贸易', '投资', '一带一路', '国际', '全球', '世界', '外资', '进出口']
        }

        theme_counts = {}
        for theme, keywords in themes.items():
            count = sum(word_freq.get(keyword, 0) for keyword in keywords)
            theme_counts[theme] = count

        sizes = list(theme_counts.values())
        labels = list(theme_counts.keys())

        plt.pie(sizes, labels=labels, autopct='%1.1f%%',
               colors=self.colors[:len(sizes)], startangle=90,
               wedgeprops=dict(width=0.5))

        plt.text(0, 0, '政府工作报告\n主题分布', ha='center', va='center',
                fontsize=14, fontweight='bold')

        plt.title('2023年政府工作报告主题内容占比分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('图表9_主题内容环形图_最终版.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("文本数据图表创建完成！")

    def generate_analysis_report(self):
        """生成分析报告"""
        report = """
=== 数据可视化分析报告 ===

【数据集1: 北京市人口数据分析】
1. 簇状柱形图分析：
   - 朝阳区人口最多（344.2万），外来人口也最多（124.3万）
   - 人口分布不均，朝阳区、海淀区、丰台区为人口聚集区
   - 外来人口与常住人口呈正相关关系

2. 饼图分析：
   - 朝阳区占全市人口比重最大，约占15.8%
   - 外来人口主要集中在朝阳、海淀、丰台等经济发达区域
   - 人口分布体现了北京城市发展的不平衡性

3. 散点图分析：
   - 常住人口与外来人口呈强正相关关系（相关系数>0.9）
   - 经济发达区域更能吸引外来人口
   - 门头沟、平谷等远郊区人口密度较低

【数据集2: 产品销售数据分析】
4. 折线图分析：
   - 1月销售额波动较大，存在明显的峰谷变化
   - 月末销售额有上升趋势，可能与促销活动相关
   - 平均销售额为519元，整体销售表现稳定

5. 箱形图分析：
   - 不同商品类别价格差异显著
   - A类商品价格相对较低且稳定
   - C类商品价格波动最大，存在高价值产品

6. 气泡图分析：
   - 销售数量与单价呈负相关趋势
   - 高价商品通常销量较少，低价商品销量较大
   - 销售额主要由单价和数量共同决定

【数据集3: 政府工作报告文本分析】
7. 词云图分析：
   - "发展"、"建设"、"推进"等词汇频率最高
   - 体现了政府工作的重点方向和政策导向
   - 经济、民生、改革等主题词突出

8. 条形图分析：
   - "发展"一词出现频率最高，体现发展主题
   - "建设"、"推进"、"加强"等动词频繁出现
   - 反映了政府工作的积极态度和行动导向

9. 环形图分析：
   - 经济发展主题占比最大，约占30%
   - 民生保障和改革创新并重，各占约25%
   - 生态环境和对外开放也是重要议题

【总结】
本次可视化分析成功展示了三个不同类型数据集的特征：
- 地理人口数据揭示了北京城市发展格局
- 时间序列销售数据展现了商业运营规律
- 文本数据分析提取了政策文件的核心内容
九种图表类型各有特色，有效传达了数据背后的信息和洞察。
        """

        print(report)

        with open('数据可视化分析报告_最终版.txt', 'w', encoding='utf-8') as f:
            f.write(report)

        print("分析报告已保存到文件！")

    def run_all(self):
        """运行所有可视化"""
        print("开始数据可视化任务（最终版 - 中文字体修复）...")

        self.load_data()
        self.create_population_charts()
        self.create_sales_charts()
        self.create_text_charts()
        self.generate_analysis_report()

        print("\n=== 所有图表创建完成！===")
        print("共生成9张图表（最终版）:")
        print("1. 图表1_北京人口簇状柱形图_最终版.png")
        print("2. 图表2_北京人口饼图_最终版.png")
        print("3. 图表3_北京人口散点图_最终版.png")
        print("4. 图表4_销售额折线图_最终版.png")
        print("5. 图表5_商品价格箱形图_最终版.png")
        print("6. 图表6_销售关系气泡图_最终版.png")
        print("7. 图表7_政府报告词云图_最终版.png")
        print("8. 图表8_高频词汇条形图_最终版.png")
        print("9. 图表9_主题内容环形图_最终版.png")
        print("\n分析报告: 数据可视化分析报告_最终版.txt")

# 主程序
if __name__ == "__main__":
    visualizer = DataVisualizerNoShow()
    visualizer.run_all()
