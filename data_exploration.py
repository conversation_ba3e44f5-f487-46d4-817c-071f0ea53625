#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集探索脚本
用于分析可用数据集的结构和内容
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from docx import Document
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def explore_excel_data(file_path, sheet_name=None):
    """探索Excel数据集"""
    print(f"\n=== 探索数据集: {file_path} ===")
    try:
        # 读取Excel文件
        if sheet_name:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        else:
            df = pd.read_excel(file_path)
        
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("\n数据类型:")
        print(df.dtypes)
        print("\n前5行数据:")
        print(df.head())
        print("\n数据统计信息:")
        print(df.describe())
        print("\n缺失值情况:")
        print(df.isnull().sum())
        
        return df
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def explore_docx_data(file_path):
    """探索Word文档数据"""
    print(f"\n=== 探索文档: {file_path} ===")
    try:
        doc = Document(file_path)
        text_content = []
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content.append(paragraph.text.strip())
        
        print(f"文档段落数: {len(text_content)}")
        print(f"总字符数: {sum(len(text) for text in text_content)}")
        print("\n前3个段落:")
        for i, text in enumerate(text_content[:3]):
            print(f"{i+1}. {text[:100]}...")
        
        return text_content
    except Exception as e:
        print(f"读取文档时出错: {e}")
        return None

def main():
    """主函数"""
    print("开始探索数据集...")
    
    # 探索数据集1: 北京市人口数据
    population_data = explore_excel_data("数据可视化数据集-A/2022年北京市各行政区常住人口.xlsx")
    
    # 探索数据集2: 产品销售数据
    sales_data = explore_excel_data("数据可视化数据集-A/产品销售统计表.xlsx")
    
    # 探索数据集3: 政府工作报告
    report_data = explore_docx_data("数据可视化数据集-A/2023年国务院政府工作报告.docx")
    
    print("\n=== 数据集探索完成 ===")
    
    return population_data, sales_data, report_data

if __name__ == "__main__":
    population_df, sales_df, report_text = main()
