<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化期末考核任务 - 展示页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        h2 {
            color: #34495e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h3 {
            color: #2980b9;
            margin-top: 30px;
        }
        .chart-section {
            margin-bottom: 50px;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .chart-item {
            text-align: center;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-item img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .chart-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        .analysis {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            text-align: left;
            font-size: 0.9em;
            color: #34495e;
        }
        .summary {
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            border-left: 5px solid #27ae60;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 数据可视化期末考核任务</h1>
        
        <div class="highlight">
            <strong>任务完成情况：</strong> 已成功为3个数据集创建9种不同类型的可视化图表，包括簇状柱形图、饼图、散点图、折线图、箱形图、气泡图、词云图、条形图和环形图。
        </div>

        <div class="chart-section">
            <h2>📊 数据集1: 2022年北京市各行政区常住人口数据</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">图表1: 簇状柱形图</div>
                    <img src="图表1_北京人口簇状柱形图.png" alt="北京人口簇状柱形图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • 朝阳区人口最多（344.2万），外来人口也最多（124.3万）<br>
                        • 人口分布不均，朝阳区、海淀区、丰台区为人口聚集区<br>
                        • 外来人口与常住人口呈正相关关系
                    </div>
                </div>
                
                <div class="chart-item">
                    <div class="chart-title">图表2: 饼图</div>
                    <img src="图表2_北京人口饼图.png" alt="北京人口饼图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • 朝阳区占全市人口比重最大，约占15.8%<br>
                        • 外来人口主要集中在朝阳、海淀、丰台等经济发达区域<br>
                        • 人口分布体现了北京城市发展的不平衡性
                    </div>
                </div>
                
                <div class="chart-item">
                    <div class="chart-title">图表3: 散点图</div>
                    <img src="图表3_北京人口散点图.png" alt="北京人口散点图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • 常住人口与外来人口呈强正相关关系（相关系数>0.9）<br>
                        • 经济发达区域更能吸引外来人口<br>
                        • 门头沟、平谷等远郊区人口密度较低
                    </div>
                </div>
            </div>
        </div>

        <div class="chart-section">
            <h2>💰 数据集2: 产品销售统计数据</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">图表4: 折线图</div>
                    <img src="图表4_销售额折线图.png" alt="销售额折线图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • 1月销售额波动较大，存在明显的峰谷变化<br>
                        • 月末销售额有上升趋势，可能与促销活动相关<br>
                        • 平均销售额为519元，整体销售表现稳定
                    </div>
                </div>
                
                <div class="chart-item">
                    <div class="chart-title">图表5: 箱形图</div>
                    <img src="图表5_商品价格箱形图.png" alt="商品价格箱形图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • 不同商品类别价格差异显著<br>
                        • A类商品价格相对较低且稳定<br>
                        • C类商品价格波动最大，存在高价值产品
                    </div>
                </div>
                
                <div class="chart-item">
                    <div class="chart-title">图表6: 气泡图</div>
                    <img src="图表6_销售关系气泡图.png" alt="销售关系气泡图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • 销售数量与单价呈负相关趋势<br>
                        • 高价商品通常销量较少，低价商品销量较大<br>
                        • 销售额主要由单价和数量共同决定
                    </div>
                </div>
            </div>
        </div>

        <div class="chart-section">
            <h2>📄 数据集3: 2023年国务院政府工作报告</h2>
            <div class="chart-grid">
                <div class="chart-item">
                    <div class="chart-title">图表7: 词云图</div>
                    <img src="图表7_政府报告词云图.png" alt="政府报告词云图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • "发展"、"建设"、"推进"等词汇频率最高<br>
                        • 体现了政府工作的重点方向和政策导向<br>
                        • 经济、民生、改革等主题词突出
                    </div>
                </div>
                
                <div class="chart-item">
                    <div class="chart-title">图表8: 条形图</div>
                    <img src="图表8_高频词汇条形图.png" alt="高频词汇条形图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • "发展"一词出现频率最高，体现发展主题<br>
                        • "建设"、"推进"、"加强"等动词频繁出现<br>
                        • 反映了政府工作的积极态度和行动导向
                    </div>
                </div>
                
                <div class="chart-item">
                    <div class="chart-title">图表9: 环形图</div>
                    <img src="图表9_主题内容环形图.png" alt="主题内容环形图">
                    <div class="analysis">
                        <strong>分析要点：</strong><br>
                        • 经济发展主题占比最大，约占30%<br>
                        • 民生保障和改革创新并重，各占约25%<br>
                        • 生态环境和对外开放也是重要议题
                    </div>
                </div>
            </div>
        </div>

        <div class="summary">
            <h2>📋 总结</h2>
            <p><strong>本次数据可视化分析成功展示了三个不同类型数据集的特征：</strong></p>
            <ul>
                <li><strong>地理人口数据</strong>：揭示了北京城市发展格局，展现了人口分布的不平衡性和外来人口的聚集特征</li>
                <li><strong>时间序列销售数据</strong>：展现了商业运营规律，分析了不同商品类别的价格分布和销售关系</li>
                <li><strong>文本数据分析</strong>：提取了政策文件的核心内容，识别了政府工作的重点主题和政策导向</li>
            </ul>
            <p><strong>九种图表类型各有特色，有效传达了数据背后的信息和洞察：</strong></p>
            <ul>
                <li>簇状柱形图、饼图、散点图适合展示人口数据的分布和关系</li>
                <li>折线图、箱形图、气泡图有效分析了销售数据的时间趋势和多维关系</li>
                <li>词云图、条形图、环形图直观展示了文本数据的词频分布和主题结构</li>
            </ul>
        </div>
    </div>
</body>
</html>
